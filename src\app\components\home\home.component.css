.container{max-width:1200px;margin:0 auto;padding:0 20px}
.hero{background:linear-gradient(135deg,#15afa7 0%,#0d8a82 100%);color:white;padding:clamp(40px, 10vw, 80px) 20px;display:flex;align-items:center;justify-content:center;min-height:clamp(400px, 70vh, 500px)}
.hero-content{flex:1;max-width:600px}
.hero-title{font-size:3.5rem;font-weight:700;margin-bottom:1rem;background:linear-gradient(45deg,#fbbf24,#f59e0b);-webkit-background-clip:text;-webkit-text-fill-color:transparent}
.hero-subtitle{font-size:1.5rem;margin-bottom:1rem;opacity:.9}
.hero-description{font-size:clamp(1rem, 2.5vw, 1.1rem);margin-bottom:2rem;opacity:.9;line-height:1.6;color:white}
.hero-buttons{display:flex;gap:1rem;flex-wrap:wrap;justify-content:center}
.btn-primary,.btn-secondary{padding:12px 24px;border:none;border-radius:8px;font-size:1rem;font-weight:600;cursor:pointer;transition:all .3s ease;min-width:140px}
.btn-primary{background:linear-gradient(45deg,#fbbf24,#f59e0b);color:white}
.btn-secondary{background:rgba(255,255,255,.2);color:white;border:2px solid rgba(255,255,255,.3)}
.btn-primary:hover,.btn-secondary:hover{transform:translateY(-2px);box-shadow:0 8px 25px rgba(0,0,0,.2)}
.hero-image{flex:1;display:flex;justify-content:center;align-items:center}
.hospital-icon{font-size:8rem;opacity:.8;animation:float 3s ease-in-out infinite}
@keyframes float{0%,100%{transform:translateY(0)}50%{transform:translateY(-20px)}}
.highlights{padding:60px 0;background:#f8fafc}
.highlights-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:2rem}
.highlight-card{background:white;padding:2rem;border-radius:12px;text-align:center;box-shadow:0 4px 20px rgba(0,0,0,.1);transition:transform .3s ease}
.highlight-card:hover{transform:translateY(-5px)}
.highlight-card h3{font-size:2rem;color:#667eea;margin-bottom:.5rem}
.highlight-card p{color:#64748b;font-size:1rem}
.specialties{padding:80px 0;background:#f9fafb}
.section-title{text-align:center;font-size:2.5rem;color:#374151;margin-bottom:3rem;font-weight:600}
.slider-container {
  position: relative;
  max-width: 900px;
  margin: 0 auto;
}

.slider {
  overflow: hidden;
  border-radius: 12px;
}

.slide-track {
  display: flex;
  transition: transform 0.5s ease-in-out;
}

.slide {
  min-width: 100%;
  padding: 0 15px;
}
.specialty-card {
  height: 300px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4px 20px rgba(0,0,0,.1);
  transition: all 0.3s ease;
}

.specialty-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, 
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(0, 0, 0, 0.4) 100%);
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.specialty-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 25px rgba(0,0,0,.15);
}

.specialty-card:hover::before {
  opacity: 0.6;
}

.specialty-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 25px;
  color: white;
  text-align: left;
  transform: translateY(5px);
  transition: transform 0.3s ease;
}

.specialty-card:hover .specialty-content {
  transform: translateY(0);
}

.specialty-content h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: white;
  text-shadow: 1px 1px 2px rgba(0,0,0,.2);
}

.specialty-content p {
  font-size: 1rem;
  line-height: 1.4;
  color: rgba(255, 255, 255, 0.95);
  margin: 0;
  max-width: 95%;
  text-shadow: 1px 1px 2px rgba(0,0,0,.2);
}
.slider-btn{position:absolute;top:50%;transform:translateY(-50%);background:rgba(107,114,128,.8);color:white;border:none;width:50px;height:50px;border-radius:50%;font-size:1.5rem;cursor:pointer;transition:all .3s ease;z-index:2}
.slider-btn:hover{background:#6b7280;transform:translateY(-50%) scale(1.05)}
.slider-btn.prev{left:-25px}
.slider-btn.next{right:-25px}
.slider-dots{display:flex;justify-content:center;gap:.5rem;margin-top:2rem}
.dot{width:12px;height:12px;border-radius:50%;background:#d1d5db;cursor:pointer;transition:all .3s ease}
.dot.active{background:#6b7280;transform:scale(1.2)}
.services-overview{padding:80px 0;background:#f8fafc}
.services-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:2rem}
.service-item{background:white;padding:2.5rem;border-radius:12px;text-align:center;box-shadow:0 4px 20px rgba(0,0,0,.1);transition:all .3s ease}
.service-item:hover{transform:translateY(-5px);box-shadow:0 8px 30px rgba(0,0,0,.15)}
.service-icon{font-size:3rem;margin-bottom:1rem}
.service-item h3{font-size:1.5rem;color:#1e293b;margin-bottom:1rem}
.service-item p{color:#64748b;line-height:1.6}
.quick-actions{padding:80px 0;background:white}
.actions-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:2rem}
.action-card{background:linear-gradient(135deg,#15afa7 0%,#0d8a82 100%);color:white;padding:2.5rem;border-radius:12px;text-align:center;transition:transform .3s ease}
.action-card:hover{transform:translateY(-5px)}
.action-icon{font-size:3rem;margin-bottom:1rem}
.action-card h3{font-size:1.5rem;margin-bottom:1rem}
.action-card p{margin-bottom:2rem;opacity:.9}
.action-btn{background:rgba(255,255,255,.2);color:white;border:2px solid rgba(255,255,255,.3);padding:10px 20px;border-radius:8px;cursor:pointer;transition:all .3s ease}
.action-btn:hover{background:white;color:#15afa7}
@media (max-width:768px){.hero{flex-direction:column;text-align:center;padding:40px 15px}.hero-title{font-size:2.5rem}.hero-buttons{justify-content:center;gap:0.5rem}.hero-buttons .btn-primary,.hero-buttons .btn-secondary{min-width:120px;padding:10px 16px;font-size:0.9rem}.slider-btn{display:none}.highlights-grid,.services-grid,.actions-grid{grid-template-columns:1fr;gap:1.5rem}.section-title{font-size:1.8rem;margin-bottom:2rem}.specialty-card{padding:2rem}.service-item{padding:2rem}.action-card{padding:2rem}.container{padding:0 15px}}
@media (max-width:480px){.hero{padding:30px 10px}.section-title{font-size:1.5rem}.specialty-card{padding:1.5rem}.service-item{padding:1.5rem}.action-card{padding:1.5rem}.hero-buttons{flex-direction:column;align-items:center}.hero-buttons .btn-primary,.hero-buttons .btn-secondary{width:100%;max-width:200px}.container{padding:0 10px}.specialties,.services-overview,.quick-actions{padding:40px 0}}