<!-- Hero Section -->
<section class="hero !bg-cover" style="background-image: url('../../../assets/bg.jpg'); background-position: center; background-size: cover;">
  <div style="background: rgba(0,0,0,0.4); padding: 20px; border-radius: 8px; max-width: 90%; margin: 0 auto;">
    <h1 style="font-size: clamp(2rem, 8vw, 4.5rem); font-weight: 800; color: white; margin-bottom: 1rem; line-height: 1.1; text-align: center;">Dhanwantari Hospital</h1>
    <p class="hero-description" style="font-size: clamp(1rem, 3vw, 1.2rem); text-align: center; margin-bottom: 2rem;">Providing world-class healthcare with modern facilities and expert medical professionals</p>
    <div class="hero-buttons" style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
      <button class="btn-primary" (click)="makeCall()" style="padding: 12px 24px; font-size: clamp(0.9rem, 2.5vw, 1rem);">Book Appointment</button>
      <button class="btn-secondary" style="padding: 12px 24px; font-size: clamp(0.9rem, 2.5vw, 1rem);">Emergency Care</button>
    </div>
  </div>
</section>

<!-- Specialties Slider -->
<section class="specialties bg-white">
  <div class="container">
    <h2 class="section-title">Our Medical Specialties</h2>
    <div class="slider-container">
      <button class="slider-btn prev" (click)="prevSlide()">‹</button>
      <div class="slider bg-white">
        <div class="slide-track" [style.transform]="'translateX(-' + (currentSlide * 100) + '%)'">
          <div class="slide" *ngFor="let specialty of specialties">
            <div class="specialty-card " [style.backgroundImage]="'url(' + specialty.image + ')'">
              <div class="specialty-content">
                <h3>{{ specialty.name }}</h3>
                <p>{{ specialty.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <button class="slider-btn next" (click)="nextSlide()">›</button>
    </div>
    <div class="slider-dots">
      <span 
        class="dot" 
        *ngFor="let specialty of specialties; let i = index"
        [class.active]="i === currentSlide"
        (click)="goToSlide(i)">
      </span>
    </div>
  </div>
</section>

<!-- Services Overview -->
<section class="services-overview">
  <div class="container">
    <h2 class="section-title">Why Choose Dhanwantari Hospital?</h2>
    <div class="services-grid">
      <div class="service-item">
        <div class="service-icon">🔬</div>
        <h3>Advanced Technology</h3>
        <p>State-of-the-art medical equipment and diagnostic tools</p>
      </div>
      <div class="service-item">
        <div class="service-icon">👨‍⚕️</div>
        <h3>Expert Doctors</h3>
        <p>Highly qualified specialists with years of experience</p>
      </div>
      <div class="service-item">
        <div class="service-icon">🏥</div>
        <h3>Modern Facilities</h3>
        <p>Comfortable rooms and world-class infrastructure</p>
      </div>
      <div class="service-item">
        <div class="service-icon">⏰</div>
        <h3>24/7 Emergency</h3>
        <p>Round-the-clock emergency and critical care services</p>
      </div>
    </div>
  </div>
</section>

<!-- Quick Actions -->
<section class="quick-actions">
  <div class="container">
    <h2 class="section-title">Quick Actions</h2>
    <div class="actions-grid">
      <div class="action-card">
        <div class="action-icon">📅</div>
        <h3>Book Appointment</h3>
        <p>Schedule your visit with our specialists</p>
        <button class="action-btn" (click)="makeCall()">Book Now</button>
      </div>
      <div class="action-card">
        <div class="action-icon">🩺</div>
        <h3>Health Checkup</h3>
        <p>Comprehensive health packages available</p>
        <button class="action-btn">View Packages</button>
      </div>
      <div class="action-card">
        <div class="action-icon">💊</div>
        <h3>Online Consultation</h3>
        <p>Consult with doctors from home</p>
        <button class="action-btn">Start Chat</button>
      </div>
    </div>
  </div>
</section>