# Dhanwantari Hospital Website

A modern, professional, and compassionate website for Dhanwantari Hospital built with Angular 17 and modern UI/UX design principles.

## 🏥 Features

- **Modern Angular 17**: Standalone components, signals, and new control flow syntax
- **Contemporary Design**: Glassmorphism effects, gradients, and micro-animations
- **Responsive Layout**: Fully responsive design that works on all devices
- **Healthcare-Focused UI**: Professional design with modern color palette
- **Interactive Features**:
  - AI Symptom Checker with disclaimer
  - Smooth scrolling navigation
  - Contact forms and appointment booking
  - Animated hero section
- **Accessibility**: High contrast colors, proper navigation
- **Performance**: Optimized for fast loading and smooth interactions

## 🚀 Live Demo

Visit the live website: [Dhanwantari Hospital](https://your-vercel-url.vercel.app)

## 🛠️ Technology Stack

- **Frontend**: Angular 17 (Standalone Components)
- **Styling**: Inline CSS with modern design patterns
- **Icons**: Emoji icons for universal compatibility
- **Deployment**: Vercel

## 📱 Sections

1. **Hero Section** - Modern animated landing with hospital motto
2. **About Us** - Hospital mission and core values (Care, Compassion, Community)
3. **Services** - Interactive grid of medical services
4. **Doctors** - Featured medical professionals
5. **Appointments** - AI Symptom Checker and booking system
6. **Blog** - Health tips and community news
7. **Contact** - Contact form and hospital information

## 🎨 Design Principles

- **Modern & Clean**: Contemporary UI with glassmorphism effects
- **Trust & Professionalism**: Medical color palette with gradients
- **Accessibility First**: High contrast, readable fonts, proper navigation
- **Mobile-First**: Responsive design that works on all devices
- **Performance**: Optimized for fast loading and smooth interactions

## 🚀 Getting Started

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/dhanwantari-hospital.git
   cd dhanwantari-hospital
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm start
   ```

4. Open your browser and navigate to `http://localhost:4200`

## 🏗️ Build

Run `npm run build` to build the project. The build artifacts will be stored in the `dist/` directory.

## 🚀 Deployment to Vercel

1. Push your code to GitHub
2. Connect your GitHub repository to Vercel
3. Vercel will automatically deploy your Angular application

### Manual Deployment
```bash
npm install -g vercel
vercel --prod
```

## 🎨 Color Palette

- **Primary Gradient**: #667eea to #764ba2
- **Accent Gold**: #fbbf24 to #f59e0b
- **Background**: #f8fafc to #e2e8f0
- **Text**: #1e293b (dark) / #64748b (medium)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the project
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📞 Contact

For any inquiries about this project, please contact:
- Email: <EMAIL>
- Phone: +91 **********

---

**Dhanwantari Hospital** - *Care. Compassion. Community.*