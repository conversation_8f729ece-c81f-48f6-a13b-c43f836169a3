<div style="font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif; background: white; min-height: 100vh;">
  <app-header></app-header>
  
  <main style="padding-top: 100px;">
    <!-- Hero Section -->
    <section style="padding: 80px 20px; background: white; text-align: center;">
      <div style="max-width: 1200px; margin: 0 auto;">
        <div style="font-size: 80px; margin-bottom: 24px;">💊</div>
        <h1 style="font-size: 48px; font-weight: 800; color: #1e293b; margin-bottom: 24px;">24-Hour Pharmacy</h1>
        <p style="font-size: 20px; color: #64748b; margin-bottom: 40px; max-width: 600px; margin-left: auto; margin-right: auto;">Your trusted pharmacy partner providing round-the-clock medication services with free home delivery</p>
        
        <div style="display: flex; gap: 20px; justify-content: center; flex-wrap: wrap;">
          <button (click)="orderMedicine()" style="background: linear-gradient(135deg, #15afa7 0%, #0d8a82 100%); color: white; padding: 16px 32px; border: none; border-radius: 50px; font-size: 18px; font-weight: 600; cursor: pointer; transition: all 0.3s;"
                  onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 25px rgba(21, 175, 167, 0.4)'"
                  onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
            📞 Order Medicine
          </button>
          <button style="background: white; color: #15afa7; border: 2px solid #15afa7; padding: 16px 32px; border-radius: 50px; font-size: 18px; font-weight: 600; cursor: pointer; transition: all 0.3s;"
                  onmouseover="this.style.background='#15afa7'; this.style.color='white'"
                  onmouseout="this.style.background='white'; this.style.color='#15afa7'">
            🚚 Track Delivery
          </button>
        </div>
      </div>
    </section>

    <!-- Medicine Categories -->
    <section style="padding: 80px 20px; background: #f8fafc;">
      <div style="max-width: 1200px; margin: 0 auto;">
        <h2 style="font-size: 36px; font-weight: 800; color: #1e293b; text-align: center; margin-bottom: 60px;">Medicine Categories</h2>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px;">
          <div *ngFor="let category of medicineCategories" style="background: white; padding: 30px; border-radius: 16px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); text-align: center; transition: transform 0.3s;"
               onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
            <div style="font-size: 40px; margin-bottom: 16px;">{{ category.icon }}</div>
            <h3 style="font-size: 20px; font-weight: 700; color: #1e293b; margin-bottom: 12px;">{{ category.name }}</h3>
            <p style="color: #64748b; font-size: 14px;">{{ category.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Services Grid -->
    <section style="padding: 80px 20px; background: white;">
      <div style="max-width: 1200px; margin: 0 auto;">
        <h2 style="font-size: 36px; font-weight: 800; color: #1e293b; text-align: center; margin-bottom: 60px;">Our Pharmacy Services</h2>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 30px;">
          <div *ngFor="let service of pharmacyServices" style="background: #f8fafc; padding: 40px; border-radius: 16px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); text-align: center;">
            <div style="font-size: 48px; margin-bottom: 20px;">{{ service.icon }}</div>
            <h3 style="font-size: 24px; font-weight: 700; color: #1e293b; margin-bottom: 16px;">{{ service.title }}</h3>
            <p style="color: #64748b; line-height: 1.6;">{{ service.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Popular Medicines -->
    <section style="padding: 80px 20px; background: #f8fafc;">
      <div style="max-width: 1200px; margin: 0 auto;">
        <h2 style="font-size: 36px; font-weight: 800; color: #1e293b; text-align: center; margin-bottom: 60px;">Popular Medicines</h2>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
          <div *ngFor="let medicine of popularMedicines" style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); text-align: center;">
            <div style="font-size: 32px; margin-bottom: 12px;">{{ medicine.icon }}</div>
            <h4 style="font-size: 16px; font-weight: 600; color: #1e293b; margin-bottom: 8px;">{{ medicine.name }}</h4>
            <p style="color: #64748b; font-size: 12px;">{{ medicine.use }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- How to Order -->
    <section style="padding: 80px 20px; background: white;">
      <div style="max-width: 1200px; margin: 0 auto;">
        <h2 style="font-size: 36px; font-weight: 800; color: #1e293b; text-align: center; margin-bottom: 60px;">How to Order</h2>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px;">
          <div *ngFor="let step of orderSteps; let i = index" style="text-align: center;">
            <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #15afa7 0%, #0d8a82 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; color: white; font-size: 32px; font-weight: bold;">{{ i + 1 }}</div>
            <h3 style="font-size: 20px; font-weight: 700; color: #1e293b; margin-bottom: 12px;">{{ step.title }}</h3>
            <p style="color: #64748b;">{{ step.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Delivery Areas -->
    <section style="padding: 80px 20px; background: #f8fafc;">
      <div style="max-width: 1200px; margin: 0 auto;">
        <h2 style="font-size: 36px; font-weight: 800; color: #1e293b; text-align: center; margin-bottom: 60px;">Delivery Areas</h2>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
          <div *ngFor="let area of deliveryAreas" style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); text-align: center;">
            <div style="font-size: 24px; margin-bottom: 12px;">📍</div>
            <h4 style="font-size: 16px; font-weight: 600; color: #1e293b; margin-bottom: 8px;">{{ area.name }}</h4>
            <p style="color: #64748b; font-size: 12px;">{{ area.time }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section style="padding: 80px 20px; background: white;">
      <div style="max-width: 800px; margin: 0 auto; text-align: center;">
        <h2 style="font-size: 36px; font-weight: 800; color: #1e293b; margin-bottom: 24px;">Need Help?</h2>
        <p style="font-size: 18px; color: #64748b; margin-bottom: 40px;">Our pharmacy team is available 24/7 to assist you with your medication needs</p>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 30px; margin-bottom: 40px;">
          <div style="background: #f8fafc; padding: 30px; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
            <div style="font-size: 32px; margin-bottom: 12px;">📞</div>
            <h4 style="font-size: 18px; font-weight: 600; color: #1e293b; margin-bottom: 8px;">Call Us</h4>
            <p style="color: #64748b;">+91 9036425149</p>
          </div>
          
          <div style="background: #f8fafc; padding: 30px; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
            <div style="font-size: 32px; margin-bottom: 12px;">💬</div>
            <h4 style="font-size: 18px; font-weight: 600; color: #1e293b; margin-bottom: 8px;">WhatsApp</h4>
            <p style="color: #64748b;">+91 9606654149</p>
          </div>
          
          <div style="background: #f8fafc; padding: 30px; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.08);">
            <div style="font-size: 32px; margin-bottom: 12px;">✉️</div>
            <h4 style="font-size: 18px; font-weight: 600; color: #1e293b; margin-bottom: 8px;">Email</h4>
            <p style="color: #64748b;" class="text-nowrap">admin&#64;dhanwantarihospitals.com</p>
          </div>
        </div>
        
        <button (click)="orderMedicine()" style="background: linear-gradient(135deg, #15afa7 0%, #0d8a82 100%); color: white; padding: 16px 40px; border: none; border-radius: 50px; font-size: 18px; font-weight: 600; cursor: pointer; transition: all 0.3s;"
                onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 25px rgba(21, 175, 167, 0.4)'"
                onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
          📞 Order Medicine Now
        </button>
      </div>
    </section>
  </main>
  
  <app-footer></app-footer>
</div>