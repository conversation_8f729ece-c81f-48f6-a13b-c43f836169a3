/* Global styles */
html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: 'Inter', sans-serif;
  overflow-x: hidden;
}

/* Global responsive utilities */
* {
  box-sizing: border-box;
}

/* Responsive text scaling */
.responsive-text {
  font-size: clamp(0.875rem, 2.5vw, 1rem);
}

.responsive-title {
  font-size: clamp(1.5rem, 5vw, 2.5rem);
}

/* Responsive spacing */
.responsive-padding {
  padding: clamp(1rem, 4vw, 2rem);
}

.responsive-margin {
  margin: clamp(1rem, 4vw, 2rem);
}

/* Mobile-first responsive breakpoints */
@media (max-width: 480px) {
  body {
    font-size: 14px;
  }
  
  section {
    padding: 30px 0 !important;
  }
  
  .container {
    padding: 0 10px !important;
  }
}

@media (max-width: 768px) {
  section {
    padding: 40px 0 !important;
  }
  
  .container {
    padding: 0 15px !important;
  }
}