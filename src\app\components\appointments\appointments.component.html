<!-- AI Symptom Checker -->
<section class="py-20 bg-gray-50">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="bg-white rounded-xl shadow-lg p-8">
      <div class="text-center mb-8">
        <span class="material-icons text-teal-600 text-5xl mb-4 block">psychology</span>
        <h2 class="text-3xl font-bold text-blue-900 mb-4">AI Symptom Checker</h2>
        <p class="text-gray-600">Get preliminary information about your symptoms</p>
      </div>
      
      <div class="max-w-2xl mx-auto">
        <textarea 
          [(ngModel)]="symptoms"
          placeholder="Describe your symptoms here..."
          class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
          rows="4">
        </textarea>
        <button 
          (click)="checkSymptoms()"
          class="w-full mt-4 bg-teal-600 hover:bg-teal-700 text-white py-3 rounded-lg font-semibold transition-colors">
          Analyze Symptoms
        </button>
        
        @if (symptomResult()) {
          <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p class="text-sm text-yellow-800 font-semibold mb-2">⚠️ Disclaimer:</p>
            <p class="text-sm text-yellow-700 mb-4">
              This is for informational purposes only and not a substitute for professional medical advice.
            </p>
            <p class="text-gray-700">{{ symptomResult() }}</p>
          </div>
        }
      </div>
    </div>
  </div>
</section>

<!-- Appointment Booking -->
<section class="py-20 bg-white">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl font-bold text-blue-900 mb-4">Book an Appointment</h2>
      <p class="text-xl text-gray-600">Schedule your visit with our medical professionals</p>
    </div>
    
    <div class="bg-gray-50 p-8 rounded-xl shadow-lg">
      <form class="grid md:grid-cols-2 gap-6">
        <div>
          <label class="block text-gray-700 font-semibold mb-2">Full Name</label>
          <input type="text" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent">
        </div>
        <div>
          <label class="block text-gray-700 font-semibold mb-2">Phone Number</label>
          <input type="tel" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent">
        </div>
        <div>
          <label class="block text-gray-700 font-semibold mb-2">Email</label>
          <input type="email" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent">
        </div>
        <div>
          <label class="block text-gray-700 font-semibold mb-2">Department</label>
          <select class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent">
            <option>Select Department</option>
            <option>Cardiology</option>
            <option>Orthopedics</option>
            <option>Pediatrics</option>
            <option>General Medicine</option>
          </select>
        </div>
        <div>
          <label class="block text-gray-700 font-semibold mb-2">Preferred Date</label>
          <input type="date" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent">
        </div>
        <div>
          <label class="block text-gray-700 font-semibold mb-2">Preferred Time</label>
          <select class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent">
            <option>Select Time</option>
            <option>9:00 AM</option>
            <option>10:00 AM</option>
            <option>11:00 AM</option>
            <option>2:00 PM</option>
            <option>3:00 PM</option>
            <option>4:00 PM</option>
          </select>
        </div>
        <div class="md:col-span-2">
          <label class="block text-gray-700 font-semibold mb-2">Reason for Visit</label>
          <textarea rows="4" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent" placeholder="Please describe your symptoms or reason for visit"></textarea>
        </div>
        <div class="md:col-span-2">
          <button type="submit" class="w-full bg-teal-600 hover:bg-teal-700 text-white py-3 rounded-lg font-semibold transition-colors">
            Book Appointment
          </button>
        </div>
      </form>
    </div>
  </div>
</section>