<!-- Blog Section -->
<section class="py-20 bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl font-bold text-blue-900 mb-4">Health & Community Blog</h2>
      <p class="text-xl text-gray-600">Stay informed with our latest health tips and community news</p>
    </div>
    
    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
      @for (post of blogPosts(); track post.id) {
        <div class="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow">
          <div class="h-48 bg-gradient-to-br from-blue-400 to-teal-500 flex items-center justify-center">
            <span class="material-icons text-white text-6xl">article</span>
          </div>
          <div class="p-6">
            <div class="flex items-center mb-2">
              <span class="bg-teal-100 text-teal-800 text-xs px-2 py-1 rounded-full">{{ post.category }}</span>
              <span class="text-gray-500 text-sm ml-2">{{ post.date }}</span>
            </div>
            <h3 class="text-xl font-bold text-blue-900 mb-2">{{ post.title }}</h3>
            <p class="text-gray-600 mb-4">{{ post.excerpt }}</p>
            <button class="text-teal-600 hover:text-teal-700 font-semibold">Read More →</button>
          </div>
        </div>
      }
    </div>
  </div>
</section>

<!-- Newsletter Signup -->
<section class="py-20 bg-blue-900 text-white">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
    <h2 class="text-3xl font-bold mb-4">Stay Updated with Health Tips</h2>
    <p class="text-xl text-blue-200 mb-8">Subscribe to our newsletter for the latest health information and hospital updates</p>
    
    <div class="max-w-md mx-auto">
      <div class="flex">
        <input type="email" placeholder="Enter your email" class="flex-1 p-3 rounded-l-lg text-gray-900 focus:outline-none">
        <button class="bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-3 rounded-r-lg font-semibold transition-colors">
          Subscribe
        </button>
      </div>
    </div>
  </div>
</section>