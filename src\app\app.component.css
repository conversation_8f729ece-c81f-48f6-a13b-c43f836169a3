/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/icon?family=Material+Icons');

/* Global styles */
* {
  font-family: 'Inter', sans-serif;
}

html {
  scroll-behavior: smooth;
}

/* Custom color variables */
:root {
  --primary-blue: #003366;
  --medical-green: #008080;
  --light-gray: #F5F5F5;
  --accent-gold: #FFD700;
}

/* Navigation enhancements */
nav {
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.95);
}

/* Hero section background */
#home {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--medical-green) 100%);
  position: relative;
}

#home::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

/* Card hover effects */
.shadow-lg:hover {
  transform: translateY(-5px);
  transition: all 0.3s ease;
}

/* Button animations */
button {
  transition: all 0.3s ease;
}

button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Testimonial cards */
.backdrop-blur-sm {
  backdrop-filter: blur(8px);
}

/* Chatbot animations */
.fixed.bottom-4.right-4 {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(100px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Form focus states */
input:focus,
textarea:focus {
  outline: none;
  border-color: var(--medical-green);
  box-shadow: 0 0 0 3px rgba(0, 128, 128, 0.1);
}

/* Responsive design enhancements */
@media (max-width: 768px) {
  .text-5xl {
    font-size: 2.5rem;
  }
  
  .text-6xl {
    font-size: 3rem;
  }
  
  .grid {
    gap: 1rem;
  }
}

/* Accessibility improvements */
a:focus,
button:focus {
  outline: 2px solid var(--accent-gold);
  outline-offset: 2px;
}

/* Loading animation for images */
.bg-gradient-to-br {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Material Icons alignment */
.material-icons {
  vertical-align: middle;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--medical-green);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #006666;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    padding: 16px 20px !important;
    flex-direction: column !important;
    gap: 20px !important;
  }
  
  .nav-links {
    gap: 20px !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
  }
  
  .hero-grid {
    grid-template-columns: 1fr !important;
    gap: 40px !important;
    text-align: center !important;
  }
  
  .hero-title {
    font-size: 40px !important;
  }
  
  .hero-buttons {
    flex-direction: column !important;
    align-items: center !important;
  }
  
  .floating-buttons {
    bottom: 20px !important;
    right: 20px !important;
    gap: 12px !important;
  }
  
  .floating-button {
    width: 48px !important;
    height: 48px !important;
    font-size: 18px !important;
  }
  
  .tooltip-text {
    display: none !important;
  }
  
  /* Health Packages Responsive */
  .packages-grid {
    grid-template-columns: 1fr !important;
    gap: 24px !important;
  }
  
  .package-card {
    padding: 24px !important;
  }
}

@media (max-width: 480px) {
  .nav-links {
    font-size: 14px !important;
  }
  
  .hero-title {
    font-size: 32px !important;
  }
  
  .hero-text {
    font-size: 16px !important;
  }
  
  .specialties-grid {
    padding: 20px !important;
  }
  
  /* Mobile Navigation */
  .nav-container {
    padding: 12px 16px !important;
  }
  
  .nav-links a {
    font-size: 13px !important;
  }
  
  /* Mobile Floating Buttons */
  .floating-buttons {
    bottom: 16px !important;
    right: 16px !important;
  }
  
  .floating-button {
    width: 44px !important;
    height: 44px !important;
    font-size: 16px !important;
  }
}