<nav style="position: fixed; top: 0; width: 100%; background: rgba(255,255,255,0.95); backdrop-filter: blur(20px); border-bottom: 1px solid rgba(0,0,0,0.1); z-index: 1000; padding: 0;">
  <div class="nav-container" style="max-width: 1400px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center; padding: 15px 20px;">
    <img src="../../assets/logo.png" style="height: 60px; width: auto;" alt="logo">
    
    <!-- Mobile Menu Button -->
    <button (click)="toggleMobileMenu()" style="display: none; background: none; border: none; font-size: 24px; cursor: pointer; color: #64748b;" class="mobile-menu-btn">
      {{ mobileMenuOpen ? '✕' : '☰' }}
    </button>
    
    <!-- Desktop Navigation -->
    <div class="desktop-nav" style="display: flex; gap: 30px; align-items: center;">
      <a (click)="scrollTo('home')" style="color: #64748b; cursor: pointer; text-decoration: none; font-weight: 500; transition: all 0.3s; position: relative;" 
         onmouseover="this.style.color='#15afa7'" onmouseout="this.style.color='#64748b'">Home</a>
      <a (click)="scrollTo('about')" style="color: #64748b; cursor: pointer; text-decoration: none; font-weight: 500; transition: all 0.3s;"
         onmouseover="this.style.color='#15afa7'" onmouseout="this.style.color='#64748b'">About</a>
      <a (click)="scrollTo('services')" style="color: #64748b; cursor: pointer; text-decoration: none; font-weight: 500; transition: all 0.3s;"
         onmouseover="this.style.color='#15afa7'" onmouseout="this.style.color='#64748b'">Services</a>
      <a (click)="scrollTo('gallery')" style="color: #64748b; cursor: pointer; text-decoration: none; font-weight: 500; transition: all 0.3s;"
         onmouseover="this.style.color='#15afa7'" onmouseout="this.style.color='#64748b'">Gallery</a>
      <a (click)="scrollTo('contact')" style="color: #64748b; cursor: pointer; text-decoration: none; font-weight: 500; transition: all 0.3s;"
         onmouseover="this.style.color='#15afa7'" onmouseout="this.style.color='#64748b'">Contact</a>
    </div>
    
    <div style="display: flex; gap: 10px; align-items: center;">
      <button (click)="goToPharmacy()" class="pharmacy-btn" style="background: linear-gradient(135deg, #e0f7f6 0%, #b2dfdb 100%); color: #15afa7; padding: 10px 20px; border: none; border-radius: 50px; font-weight: 600; cursor: pointer; transition: all 0.3s; box-shadow: 0 4px 15px rgba(21, 175, 167, 0.2); display: flex; align-items: center; gap: 8px; font-size: 14px;"
              onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 25px rgba(21, 175, 167, 0.3)'"
              onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(21, 175, 167, 0.2)'">
        💊 <span class="btn-text">Pharmacy</span>
      </button>
      <button (click)="makeCall()" class="cta-btn" style="background: linear-gradient(135deg, #15afa7 0%, #0d8a82 100%); color: white; padding: 10px 20px; border: none; border-radius: 50px; font-weight: 600; cursor: pointer; transition: all 0.3s; box-shadow: 0 4px 15px rgba(21, 175, 167, 0.4); display: flex; align-items: center; gap: 8px; font-size: 14px;"
              onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 25px rgba(21, 175, 167, 0.6)'"
              onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(21, 175, 167, 0.4)'">
        📞 <span class="btn-text">Book Appointment</span>
      </button>
    </div>
  </div>
  
  <!-- Mobile Navigation Menu -->
  <div *ngIf="mobileMenuOpen" style="background: white; border-top: 1px solid rgba(0,0,0,0.1); padding: 20px; display: none;" class="mobile-nav">
    <div style="display: flex; flex-direction: column; gap: 20px;">
      <a (click)="scrollTo('home'); toggleMobileMenu()" style="color: #64748b; cursor: pointer; text-decoration: none; font-weight: 500; padding: 10px 0; border-bottom: 1px solid #f1f5f9;">Home</a>
      <a (click)="scrollTo('about'); toggleMobileMenu()" style="color: #64748b; cursor: pointer; text-decoration: none; font-weight: 500; padding: 10px 0; border-bottom: 1px solid #f1f5f9;">About</a>
      <a (click)="scrollTo('services'); toggleMobileMenu()" style="color: #64748b; cursor: pointer; text-decoration: none; font-weight: 500; padding: 10px 0; border-bottom: 1px solid #f1f5f9;">Services</a>
      <a (click)="scrollTo('gallery'); toggleMobileMenu()" style="color: #64748b; cursor: pointer; text-decoration: none; font-weight: 500; padding: 10px 0; border-bottom: 1px solid #f1f5f9;">Gallery</a>
      <a (click)="scrollTo('contact'); toggleMobileMenu()" style="color: #64748b; cursor: pointer; text-decoration: none; font-weight: 500; padding: 10px 0;">Contact</a>
    </div>
  </div>
</nav>

<style>
@media (max-width: 768px) {
  .mobile-menu-btn { display: block !important; }
  .desktop-nav { display: none !important; }
  .mobile-nav { display: block !important; }
  .cta-btn .btn-text, .pharmacy-btn .btn-text { display: none; }
  .cta-btn, .pharmacy-btn { padding: 10px 12px !important; }
}

@media (max-width: 480px) {
  .nav-container { padding: 10px 15px !important; }
  .cta-btn { font-size: 12px !important; }
}
</style>