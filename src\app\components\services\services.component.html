<!-- Services Section -->
<section class="py-20 bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16">
      <h2 class="text-4xl font-bold text-blue-900 mb-4">Our Services</h2>
      <p class="text-xl text-gray-600">Comprehensive healthcare services for all your medical needs</p>
    </div>
    
    <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
      @for (service of services(); track service.id) {
        <div class="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
          <span class="material-icons text-teal-600 text-4xl mb-4 block">{{ service.icon }}</span>
          <h3 class="text-xl font-bold text-blue-900 mb-2">{{ service.name }}</h3>
          <p class="text-gray-600">{{ service.description }}</p>
        </div>
      }
    </div>
    
    <div class="text-center mt-12">
      <button class="bg-teal-600 hover:bg-teal-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
        View All Services
      </button>
    </div>
  </div>
</section>